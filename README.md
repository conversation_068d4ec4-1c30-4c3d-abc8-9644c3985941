# AlphaPebble Spark Hub

## Project Info

A modern web application built with React, TypeScript, and Tailwind CSS.

## How to Edit This Code

There are several ways to work with this codebase:

**Use Your Preferred IDE**

To work locally using your IDE:

1. Make sure you have Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

2. Follow these steps:

```sh
# Clone the repository
git clone <YOUR_GIT_URL>

# Navigate to the project directory
cd alphapebble-spark-hub

# Install dependencies
npm install

# Start the development server
npm run dev
```
