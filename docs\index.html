<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AlphaPebble Consulting | Startup Experiments & Innovation</title>
    <meta
      name="description"
      content="AlphaPebble Consulting helps startups validate ideas through rapid experiments. Our expert team specializes in AI/ML, data engineering, and full-stack development." />
    <style>
      /* Basic styling */
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
          'Open Sans', 'Helvetica Neue', sans-serif;
        margin: 0;
        padding: 0;
        background-color: #1a1f2c;
        color: #f6f6f7;
        line-height: 1.6;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      header {
        background-color: #1a1f2c;
        padding: 20px 0;
        position: fixed;
        width: 100%;
        top: 0;
        z-index: 100;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      }

      nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 15px;
        text-decoration: none;
      }

      .logo img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
      }

      .logo span {
        font-size: 1.5rem;
        font-weight: 500;
        color: #f6f6f7;
      }

      .nav-links {
        display: flex;
        gap: 30px;
      }

      .nav-links a {
        color: #f6f6f7;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.3s;
      }

      .nav-links a:hover {
        color: #3b82f6;
      }

      .hero {
        padding-top: 120px;
        padding-bottom: 80px;
        text-align: center;
      }

      .hero img {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        margin-bottom: 30px;
      }

      .hero h1 {
        font-size: 3rem;
        margin-bottom: 20px;
        line-height: 1.2;
      }

      .hero p {
        font-size: 1.25rem;
        max-width: 800px;
        margin: 0 auto 40px;
        color: rgba(246, 246, 247, 0.8);
      }

      .highlight {
        color: #3b82f6;
      }

      .btn {
        display: inline-block;
        padding: 12px 24px;
        background-color: #3b82f6;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        font-weight: 500;
        transition: background-color 0.3s;
        margin: 10px;
      }

      .btn:hover {
        background-color: #2563eb;
      }

      .btn-outline {
        background-color: transparent;
        border: 1px solid rgba(246, 246, 247, 0.2);
      }

      .btn-outline:hover {
        background-color: rgba(246, 246, 247, 0.1);
      }

      .services {
        padding: 80px 0;
        background-color: #111827;
      }

      .section-title {
        text-align: center;
        margin-bottom: 60px;
      }

      .section-title h2 {
        font-size: 2.5rem;
        margin-bottom: 20px;
      }

      .section-title p {
        font-size: 1.25rem;
        max-width: 800px;
        margin: 0 auto;
        color: rgba(246, 246, 247, 0.8);
      }

      .service-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
      }

      .service-card {
        background-color: rgba(31, 41, 55, 0.5);
        border: 1px solid #374151;
        border-radius: 8px;
        padding: 30px;
      }

      .service-icon {
        width: 50px;
        height: 50px;
        background-color: rgba(59, 130, 246, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
      }

      .service-icon svg {
        width: 24px;
        height: 24px;
        color: #3b82f6;
      }

      .service-card h3 {
        font-size: 1.5rem;
        margin-bottom: 15px;
      }

      .service-card p {
        color: rgba(246, 246, 247, 0.7);
      }

      .process {
        padding: 80px 0;
      }

      .process-steps {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 30px;
        margin-top: 60px;
      }

      .process-step {
        background-color: rgba(31, 41, 55, 0.3);
        border: 1px solid #374151;
        border-radius: 8px;
        padding: 30px;
        position: relative;
      }

      .step-number {
        position: absolute;
        top: -20px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 40px;
        background-color: #3b82f6;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1.25rem;
      }

      .process-step h3 {
        margin-top: 10px;
        font-size: 1.5rem;
        margin-bottom: 15px;
      }

      .process-step p {
        color: rgba(246, 246, 247, 0.7);
      }

      .cta {
        padding: 80px 0;
        background-color: #111827;
        text-align: center;
      }

      footer {
        background-color: #0f172a;
        padding: 40px 0;
      }

      .footer-content {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .footer-logo {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 30px;
      }

      .footer-logo img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
      }

      .footer-logo span {
        font-size: 1.5rem;
        font-weight: 500;
        color: #f6f6f7;
      }

      .footer-links {
        display: flex;
        gap: 30px;
        margin-bottom: 30px;
      }

      .footer-links a {
        color: rgba(246, 246, 247, 0.8);
        text-decoration: none;
        transition: color 0.3s;
      }

      .footer-links a:hover {
        color: #f6f6f7;
      }

      .copyright {
        color: rgba(246, 246, 247, 0.6);
        text-align: center;
        padding-top: 20px;
        border-top: 1px solid #374151;
      }

      /* Responsive adjustments */
      @media (max-width: 768px) {
        .nav-links {
          display: none;
        }

        .hero h1 {
          font-size: 2.5rem;
        }

        .section-title h2 {
          font-size: 2rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="container">
        <nav>
          <a href="#" class="logo">
            <img src="./images/placeholder-logo.svg" alt="AlphaPebble Logo" />
            <span>AlphaPebble</span>
          </a>
          <div class="nav-links">
            <a href="#services">Services</a>
            <a href="#process">Our Approach</a>
            <a href="#contact">Contact</a>
          </div>
        </nav>
      </div>
    </header>

    <section class="hero">
      <div class="container">
        <img src="./images/placeholder-logo.svg" alt="AlphaPebble Logo" />
        <h1>
          Turning Startup Ideas into <br /><span class="highlight">Successful Experiments</span>
        </h1>
        <p>
          We help startups validate ideas quickly through tiny, focused experiments. Our expert
          consulting team turns concepts into reality with minimal risk.
        </p>
        <div>
          <a href="#services" class="btn">Our Services</a>
          <a href="#contact" class="btn btn-outline">Get in Touch</a>
        </div>
      </div>
    </section>

    <section id="services" class="services">
      <div class="container">
        <div class="section-title">
          <h2>How We Help Startups Succeed</h2>
          <p>
            Our specialized consulting services are designed to help you validate ideas and build
            successful products.
          </p>
        </div>
        <div class="service-grid">
          <div class="service-card">
            <div class="service-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
            <h3>Idea Validation</h3>
            <p>Test your startup concepts with minimal investment before committing resources.</p>
          </div>
          <div class="service-card">
            <div class="service-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3>Rapid Prototyping</h3>
            <p>
              Transform ideas into working prototypes in days, not months, with our agile approach.
            </p>
          </div>
          <div class="service-card">
            <div class="service-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
              </svg>
            </div>
            <h3>Technical Development</h3>
            <p>
              Our team of experienced engineers can build your product using the latest
              technologies.
            </p>
          </div>
          <div class="service-card">
            <div class="service-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <h3>Growth Strategy</h3>
            <p>Scale successful experiments with our proven frameworks for sustainable growth.</p>
          </div>
        </div>
      </div>
    </section>

    <section id="process" class="process">
      <div class="container">
        <div class="section-title">
          <h2>Our Approach to Tiny Experiments</h2>
          <p>
            We follow a proven methodology to turn your ideas into successful experiments with
            minimal risk and maximum learning.
          </p>
        </div>
        <div class="process-steps">
          <div class="process-step">
            <div class="step-number">1</div>
            <h3>Discovery</h3>
            <p>We analyze your idea, identify assumptions, and define clear success metrics.</p>
          </div>
          <div class="process-step">
            <div class="step-number">2</div>
            <h3>Design</h3>
            <p>
              We create a minimal experiment design that tests your core hypothesis efficiently.
            </p>
          </div>
          <div class="process-step">
            <div class="step-number">3</div>
            <h3>Build</h3>
            <p>
              Our team rapidly develops the experiment using the right technologies for your needs.
            </p>
          </div>
          <div class="process-step">
            <div class="step-number">4</div>
            <h3>Analyze</h3>
            <p>We measure results, extract insights, and recommend next steps for your business.</p>
          </div>
        </div>
      </div>
    </section>

    <section id="contact" class="cta">
      <div class="container">
        <div class="section-title">
          <h2>Ready to Validate Your Startup Idea?</h2>
          <p>Let's discuss how we can help you test your concept with a focused experiment.</p>
        </div>
        <div>
          <a href="mailto:<EMAIL>" class="btn">Schedule a Consultation</a>
        </div>
      </div>
    </section>

    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <img src="./images/placeholder-logo.svg" alt="AlphaPebble Logo" />
            <span>AlphaPebble</span>
          </div>
          <div class="footer-links">
            <a href="#services">Services</a>
            <a href="#process">Our Approach</a>
            <a href="#contact">Contact</a>
          </div>
          <div class="copyright">
            <p>© 2024 AlphaPebble Consulting. All rights reserved.</p>
          </div>
        </div>
      </div>
    </footer>
  </body>
</html>
